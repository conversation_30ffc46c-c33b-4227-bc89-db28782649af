#home {
    @apply justify-center items-center;

    .hero-section {
        @apply animate-[fade-in_1s_ease-in-out] py-16 overflow-hidden mx-auto md:max-w-[80%];

        .hero-wrapper {
            @apply flex flex-col lg:flex-row items-center;
        }

        .hero-content {
            @apply mb-2.5;

            p {
                @apply text-lg mt-4 text-start;
            }

            .hero-buttons {
                @apply mt-10 flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-4;

                i {
                    @apply mr-2;
                }
            }
        }

        .floating-image {
            @apply animate-[float_6s_ease-in-out_infinite] w-full md:w-1/2 mx-auto mt-30;
        }
    }
}

#highlight-section {
    @apply md:max-w-[80%] lg:max-w-[60%] mx-auto my-15;

    #featured-card {
        @apply bg-[var(--background-secondary)] rounded-3xl shadow p-6;

        #card-header {
            @apply mb-4 text-center;

            h2 {
                @apply text-3xl font-bold text-[var(--text-primary)];
            }

            p {
                @apply text-lg text-[var(--text-secondary)];
            }
        }

        #highlightContainer {
            @apply mb-4;

            iframe {
                @apply rounded-lg aspect-video w-full;
            }

            .highlight-wrapper {
                @apply w-full rounded-lg shadow;

                .video-container {
                    @apply relative aspect-video rounded-t-lg;

                    iframe {
                        @apply absolute top-0 left-0 w-full h-full rounded-t-lg;
                    }

                    .placeholder-content {
                        @apply absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center bg-black/80 text-white p-5 text-center;

                        .placeholder-icon {
                            @apply text-5xl mb-4;
                            color: var(--is-red-55);
                        }

                        p {
                            @apply mb-6 text-lg;
                        }

                        .load-video-btn {
                            @apply hover:bg-red-700 text-white px-6 py-3 rounded-full transition-all duration-300 transform hover:scale-105 flex items-center;
                            background-color: var(--is-red-55);

                            i {
                                @apply mr-2;
                            }
                        }
                    }
                }
            }

            .highlight-loading {
                @apply flex flex-col items-center justify-center p-10 text-center;
                min-height: 250px;

                .loading-spinner {
                    @apply text-4xl text-red-600 mb-4;
                }

                p {
                    @apply text-[var(--text-secondary)];
                }
            }

            .highlight-error {
                @apply flex flex-col items-center justify-center p-10 text-center min-h-[250px];

                .error-icon {
                    @apply text-4xl text-red-600 mb-4;
                }

                p {
                    @apply text-[var(--text-secondary)] mb-6;
                }

                .retry-btn {
                    @apply bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors flex items-center;

                    i {
                        @apply mr-2;
                    }
                }
            }
        }

        .button-container {
            @apply text-center;

            button {
                @apply inline-flex items-center px-5 py-2.5 w-full justify-center text-sm font-medium text-center text-white bg-red-700 hover:bg-red-800 dark:bg-red-600 dark:hover:bg-red-700 rounded-lg focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800;

                i {
                    @apply mr-2;
                }

                p {
                    @apply text-lg font-medium;
                }
            }
        }
    }
}

#social-proof {
    @apply pb-5;

    #statsContainer {
        @apply flex flex-row justify-center flex-wrap gap-2.5;

        .stats-card {
            @apply text-center transition duration-300 ease-in-out my-auto p-8 rounded-3xl shadow-lg lg:max-w-70 md:max-w-50 w-full;
            background-color: var(--background-secondary);
            color: var(--text-primary);

            i {
                color: var(--is-red-55);
                @apply text-5xl;
            }

            .counter {
                @apply text-4xl font-bold my-4;
            }
        }
    }
}