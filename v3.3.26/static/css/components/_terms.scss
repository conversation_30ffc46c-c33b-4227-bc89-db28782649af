#terms {
    .tos-section {
        @apply py-8 px-4;

        .tos-container {
            @apply max-w-screen-xl mx-auto;

            .header-content {
                @apply mb-8 text-center;

                .description {
                    @apply text-xl mt-3;
                    color: var(--text-primary);
                }

                .date {
                    @apply mt-2;
                    color: var(--is-red-60);
                }
            }

            .cards-grid {
                @apply grid md:grid-cols-2 lg:grid-cols-3 gap-8;

                .tos-card {
                    @apply p-6 rounded-lg shadow-lg;
                    background-color: var(--background-secondary);

                    .card-header {
                        @apply flex items-center mb-4;

                        i {
                            @apply text-2xl mr-3;
                            color: var(--text-secondary);
                        }

                        h2 {
                            @apply text-xl font-bold;
                            color: var(--text-primary);
                        }
                    }

                    p {
                        color: var(--text-secondary);
                    }
                }
            }
        }
    }
}