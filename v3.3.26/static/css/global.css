@import './container/_root.scss';

@layer theme, base, components, utilities;

@import "tailwindcss";

@import "flowbite/src/themes/default";

@plugin "flowbite/plugin";

@source "../../node_modules/flowbite";

@import './container/_navbar.scss';

@import './container/_main.scss';

@import './components/_home.scss';

@import './components/_streaming.scss';

@import './components/_about.scss';

@import './components/_terms.scss';

@import './components/_privacy.scss';

@import './container/_footer.scss';

@import './utils/_theme-selector.scss';

@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *));