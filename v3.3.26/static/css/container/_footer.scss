footer {
    background-color: var(--background-secondary);
    color: var(--text-primary);
    @apply p-3;

    h4 {
        @apply mb-3 mt-3 text-2xl font-bold cursor-default;

        &:hover {
            color: var(--is-red-55);
        }
    }

    #desktopFooter {
        @apply hidden md:block;

        #footer-content,
        #footer-bottom {
            @apply grid grid-cols-1 gap-1 md:grid-cols-2 m-1 md:m-5;

            #quick-links {
                .f-link {
                    @apply hover:text-red-600 hover:font-bold cursor-pointer;
                }
            }

            #social-links {
                #social-icons {
                    @apply flex gap-5 text-4xl md:text-3xl;

                    & .fa-x-twitter {
                        @apply text-sky-600 dark:text-sky-400;
                    }

                    & .fa-soundcloud {
                        color: var(--color-amber-600);
                    }

                    & .fa-youtube {
                        color: #f03;
                    }

                    & .fa-spotify {
                        color: var(--color-green-400);
                    }

                    & :hover {
                        &.fa-x-twitter {
                            @apply text-sky-500;
                        }

                        &.fa-soundcloud {
                            @apply text-orange-500;
                        }

                        &.fa-youtube {
                            @apply text-red-700;
                        }

                        &.fa-spotify {
                            @apply text-green-500;
                        }
                    }
                }
            }

            #theme-container div button {
                @apply text-4xl md:text-3xl;
            }

            #language-container {
                @apply pb-2;

                #language {

                    & .langItem {
                        @apply grid items-center justify-start;

                        & .fi {
                            @apply text-3xl md:text-2xl transition-all ease-in-out duration-400 cursor-pointer saturate-0 hover:saturate-100;

                            &.active {
                                @apply text-4xl md:text-3xl saturate-100;
                            }
                        }

                        & p {
                            @apply text-sm md:text-base lg:text-lg fixed left-5/4 bottom-1 w-20;
                        }
                    }

                }
            }
        }

        #copyright {
            #f-divider {
                @apply my-2.5 opacity-25;
            }

            p {
                @apply text-[var(--text-secondary)] mb-2.5 font-light;
            }
        }
    }
}