I need you to update the current SCSS styles in this project by referencing the styles from `/home/<USER>/Documentos/GitHub/isSTAR-P/output.css`. This output.css file comes from my most recent project which has similar styling but with some differences - the current project uses only HTML, CSS, and JavaScript while the reference project is a Flask application.

Please:
1. First, examine the `/home/<USER>/Documentos/GitHub/isSTAR-P/output.css` file to understand the target styles
2. Find all SCSS files in the current project that contain styles
3. Update each SCSS file to match the styling patterns and design from the output.css file
4. Account for the architectural differences between a plain HTML/CSS/JS project and a Flask project when making the updates
5. Ensure the updated styles maintain compatibility with the current project structure

Focus on updating the existing SCSS files rather than replacing them entirely, and make sure the changes are appropriate for the current project's architecture.